import dotenv from "dotenv";
dotenv.config();

import nvdChat from "./utils/nvd-ai-function-calling.js";

// Simple query
const result = await nvdChat.queryNVD(
  "What are the latest critical vulnerabilities from the last 7 days?"
);
console.log(result.response);

// Full conversation
const messages = [
  {
    role: "user",
    content: "Tell me about the Log4j vulnerability CVE-2021-44228",
  },
];

const conversation = await nvdChat.handleNVDChat(messages);
console.log(conversation.response);
