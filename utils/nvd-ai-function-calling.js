import { OpenAI } from "openai";
import * as nvdClient from "./nvd-utils.js";
import { env } from "./env.js";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY || "dummy-key-for-testing",
});

/**
 * NVD function definitions for OpenAI function calling
 */
const nvdTools = [
  {
    type: "function",
    name: "get_cve_by_id",
    description:
      "Get detailed information about a specific CVE vulnerability by its ID",
    parameters: {
      type: "object",
      properties: {
        cveId: {
          type: "string",
          description: "CVE identifier (e.g., 'CVE-2021-44228')",
        },
      },
      required: ["cveId"],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "search_cves_by_keyword",
    description: "Search for CVE vulnerabilities using keywords or phrases",
    parameters: {
      type: "object",
      properties: {
        keyword: {
          type: "string",
          description:
            "Search keyword or phrase (e.g., 'log4j', 'remote code execution')",
        },
        exactMatch: {
          type: "boolean",
          description: "Whether to match the exact phrase (default: false)",
          default: false,
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: ["keyword"],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_cves_by_severity",
    description: "Get CVE vulnerabilities filtered by CVSS severity level",
    parameters: {
      type: "object",
      properties: {
        severity: {
          type: "string",
          enum: ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
          description: "CVSS severity level",
        },
        version: {
          type: "string",
          enum: ["v2", "v3", "v4"],
          description: "CVSS version (default: v3)",
          default: "v3",
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: ["severity"],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_recent_cves",
    description: "Get recently published CVE vulnerabilities",
    parameters: {
      type: "object",
      properties: {
        days: {
          type: "number",
          description: "Number of days to look back (default: 7)",
          default: 7,
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: [],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_cves_by_cpe",
    description:
      "Get CVE vulnerabilities associated with a specific product or technology",
    parameters: {
      type: "object",
      properties: {
        cpeName: {
          type: "string",
          description:
            "CPE name string (e.g., 'cpe:2.3:a:apache:log4j:*:*:*:*:*:*:*:*')",
        },
        vulnerableOnly: {
          type: "boolean",
          description: "Only return vulnerable configurations (default: false)",
          default: false,
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: ["cpeName"],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_cves_by_cwe",
    description:
      "Get CVE vulnerabilities by Common Weakness Enumeration (CWE) type",
    parameters: {
      type: "object",
      properties: {
        cweId: {
          type: "string",
          description:
            "CWE identifier (e.g., 'CWE-79' for XSS, 'CWE-89' for SQL injection)",
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: ["cweId"],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_high_severity_kev_cves",
    description:
      "Get high-severity CVEs that are in CISA's Known Exploited Vulnerabilities catalog",
    parameters: {
      type: "object",
      properties: {
        limit: {
          type: "number",
          description: "Maximum number of results to return (default: 10)",
          default: 10,
        },
      },
      required: [],
      additionalProperties: false,
    },
  },
  {
    type: "function",
    name: "get_cve_change_history",
    description: "Get change history for a specific CVE",
    parameters: {
      type: "object",
      properties: {
        cveId: {
          type: "string",
          description: "CVE identifier to get change history for",
        },
      },
      required: ["cveId"],
      additionalProperties: false,
    },
  },
];

/**
 * Function implementations that call the NVD API
 */
const functionImplementations = {
  async get_cve_by_id({ cveId }) {
    try {
      const result = await nvdClient.getCVEById(cveId);
      return {
        success: true,
        data: result,
        message: result ? `Found CVE ${cveId}` : `CVE ${cveId} not found`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to retrieve CVE ${cveId}`,
      };
    }
  },

  async search_cves_by_keyword({ keyword, exactMatch = false, limit = 10 }) {
    try {
      const result = await nvdClient.searchCVEsByKeyword(
        keyword,
        exactMatch,
        limit
      );
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs matching "${keyword}"`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to search for "${keyword}"`,
      };
    }
  },

  async get_cves_by_severity({ severity, version = "v3", limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsBySeverity(
        severity,
        version,
        limit
      );
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs with ${severity} severity (CVSS ${version})`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs with ${severity} severity`,
      };
    }
  },

  async get_recent_cves({ days = 7, limit = 10 }) {
    try {
      const result = await nvdClient.getRecentCVEs(days, limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs published in the last ${days} days`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get recent CVEs`,
      };
    }
  },

  async get_cves_by_cpe({ cpeName, vulnerableOnly = false, limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsByCPE(
        cpeName,
        vulnerableOnly,
        limit
      );
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs for ${cpeName}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs for ${cpeName}`,
      };
    }
  },

  async get_cves_by_cwe({ cweId, limit = 10 }) {
    try {
      const result = await nvdClient.getCVEsByCWE(cweId, limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} CVEs with weakness ${cweId}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get CVEs for ${cweId}`,
      };
    }
  },

  async get_high_severity_kev_cves({ limit = 10 }) {
    try {
      const result = await nvdClient.getHighSeverityKEVCVEs(limit);
      return {
        success: true,
        data: {
          totalResults: result.totalResults,
          vulnerabilities: result.vulnerabilities,
        },
        message: `Found ${result.totalResults} high-severity CVEs in CISA's KEV catalog`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get KEV CVEs`,
      };
    }
  },

  async get_cve_change_history({ cveId }) {
    try {
      const result = await nvdClient.getCVEChangeHistoryById(cveId);
      return {
        success: true,
        data: result,
        message: `Retrieved change history for ${cveId}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get change history for ${cveId}`,
      };
    }
  },
};

/**
 * Format CVE data for display
 * @param {Object} cve - CVE object from NVD API
 * @returns {string} Formatted CVE information
 */
function formatCVEForDisplay(cve) {
  if (!cve || !cve.cve) return "No CVE data available";

  const { cve: cveData } = cve;
  const description =
    cveData.descriptions?.find((d) => d.lang === "en")?.value ||
    "No description available";
  const cvssV3 = cve.cve.metrics?.cvssMetricV3?.[0]?.cvssData;
  const cvssV2 = cve.cve.metrics?.cvssMetricV2?.[0]?.cvssData;

  let formatted = `**${cveData.id}**\n`;
  formatted += `Description: ${description}\n`;
  formatted += `Published: ${new Date(
    cveData.published
  ).toLocaleDateString()}\n`;
  formatted += `Last Modified: ${new Date(
    cveData.lastModified
  ).toLocaleDateString()}\n`;

  if (cvssV3) {
    formatted += `CVSS v3.1 Score: ${cvssV3.baseScore} (${cvssV3.baseSeverity})\n`;
    formatted += `Vector: ${cvssV3.vectorString}\n`;
  } else if (cvssV2) {
    formatted += `CVSS v2 Score: ${cvssV2.baseScore} (${cvssV2.baseSeverity})\n`;
    formatted += `Vector: ${cvssV2.vectorString}\n`;
  }

  return formatted;
}

/**
 * Main function to handle NVD chat conversations
 * @param {Array} messages - Array of conversation messages
 * @param {Object} options - Additional options
 * @param {string} [options.model='gpt-4'] - OpenAI model to use
 * @param {number} [options.maxTokens=4000] - Maximum tokens for response
 * @returns {Promise<Object>} Response from the assistant
 */
async function handleNVDChat(messages, options = {}) {
  const { model = "gpt-4", maxTokens = 4000 } = options;

  try {
    // Add system message if not present
    const systemMessage = {
      role: "system",
      content: `You are a cybersecurity assistant that helps users query the National Vulnerability Database (NVD). 
      You can search for CVE vulnerabilities, get vulnerability details, check severity levels, and provide security insights.
      
      When presenting CVE information:
      - Always include the CVE ID, description, and severity score when available
      - Explain the potential impact and risk level
      - Suggest remediation steps when appropriate
      - Format the response in a clear, readable manner
      
      Use the available functions to fetch real-time vulnerability data from the NVD API.`,
    };

    const conversationMessages =
      messages[0]?.role === "system" ? messages : [systemMessage, ...messages];

    const response = await openai.chat.completions.create({
      model,
      messages: conversationMessages,
      tools: nvdTools,
      tool_choice: "auto",
      max_tokens: maxTokens,
      temperature: 0.1,
    });

    const message = response.choices[0].message;

    // Handle function calls
    if (message.tool_calls) {
      const functionResults = [];

      for (const toolCall of message.tool_calls) {
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);

        console.log(
          `Calling function: ${functionName} with args:`,
          functionArgs
        );

        if (functionImplementations[functionName]) {
          const result = await functionImplementations[functionName](
            functionArgs
          );
          functionResults.push({
            tool_call_id: toolCall.id,
            role: "tool",
            content: JSON.stringify(result),
          });
        } else {
          functionResults.push({
            tool_call_id: toolCall.id,
            role: "tool",
            content: JSON.stringify({
              success: false,
              error: `Function ${functionName} not implemented`,
            }),
          });
        }
      }

      // Continue conversation with function results
      const followUpMessages = [
        ...conversationMessages,
        message,
        ...functionResults,
      ];

      const followUpResponse = await openai.chat.completions.create({
        model,
        messages: followUpMessages,
        max_tokens: maxTokens,
        temperature: 0.1,
      });

      return {
        success: true,
        response: followUpResponse.choices[0].message.content,
        functionCalls: message.tool_calls.map((tc) => ({
          function: tc.function.name,
          arguments: JSON.parse(tc.function.arguments),
        })),
        usage: {
          initial: response.usage,
          followUp: followUpResponse.usage,
        },
      };
    }

    return {
      success: true,
      response: message.content,
      functionCalls: [],
      usage: response.usage,
    };
  } catch (error) {
    console.error("Error in NVD chat handler:", error);
    return {
      success: false,
      error: error.message,
      response:
        "I encountered an error while processing your request. Please try again.",
    };
  }
}

/**
 * Convenience function for single message queries
 * @param {string} userMessage - User's message/query
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Response from the assistant
 */
async function queryNVD(userMessage, options = {}) {
  const messages = [{ role: "user", content: userMessage }];
  return handleNVDChat(messages, options);
}

export default {
  handleNVDChat,
  queryNVD,
  nvdTools,
  functionImplementations,
  formatCVEForDisplay,
};

// Example usage:
/*
const nvdChat = require('./nvd-chat-assistant');

// Single query
const result = await nvdChat.queryNVD("What are the latest critical vulnerabilities?");
console.log(result.response);

// Conversation
const messages = [
  { role: "user", content: "Tell me about CVE-2021-44228" },
  { role: "assistant", content: "Let me look that up for you..." },
  { role: "user", content: "What's the severity and how can I fix it?" }
];

const conversation = await nvdChat.handleNVDChat(messages);
console.log(conversation.response);
*/
